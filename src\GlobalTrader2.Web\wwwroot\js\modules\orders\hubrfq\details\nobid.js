import { LiteFormDialog } from "../../../../components/base/lite-form-dialog.js?v=#{BuildVersion}#";

export class NoBidHandler {
    constructor() {
        this.form = null;
        this.init();
    }

    init() {
        const self = this; // Store reference to access form instance
        this.form = new LiteFormDialog("#hubrfq-no-bid-dialog", {
            width: '800px',
            closeWhenSuccess: true,
            url: "/api/orders/bom/no-bid",
            method: 'PUT',
            buttons: [
                { name: "save", icon: "check", alt: "yes", display: 'Yes' },
                { name: "cancel", icon: "xmark", alt: "no", display: 'No' }
            ],
            validationRules: {
                bidNotes: {
                    required: true,
                }
            },
            errorPlacement: function (error, element) {
                const errorSummary = $("#hubrfq-no-bid-dialog .form-error-summary");
                error.insertAfter(element);
                errorSummary.show();
            },
            body: function () {
                const data = self.form.getFormData();
                return {
                    bomId: data.id,
                    bomCode: data.code,
                    bomName: data.name,
                    bomCompanyName: data.company,
                    bomCompanyNo: data.companyNo,
                    salesManNo: Number(data.reqSalesPerson.replace('||', '')),
                    customerRequirementId: data.customerRequirementId,
                    noBidNotes: data.bidNotes
                };
            }
        });

        this.form.on('dialogopen', () => {
            $("#hubrfq-no-bid-dialog .form-error-summary").hide();
        });

        // this.form.$form.on('closedWithSuccessedResponse.mf', (e, { response }) => {
        //     $('#nobid-btn').prop('disabled', true);
        // });
    }
}
