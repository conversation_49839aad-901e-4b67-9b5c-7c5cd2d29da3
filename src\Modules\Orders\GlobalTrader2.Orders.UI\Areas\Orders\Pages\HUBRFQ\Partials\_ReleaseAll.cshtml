@using Microsoft.Extensions.Localization
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.HUBRFQ.Details
@using GlobalTrader2.SharedUI.Helper

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer

@model IndexModel

@{
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
    var accordionHeaderIconClass = HtmlHelperExtensions.GetAccordionHeaderIconClass();
}

<div id="hubrfq-release-all-dialog" class="dialog-container" title="@_localizer["ReleaseAllTitle"]" style="display: none;">
    <div class="dialog-description">
        <span>@_localizer["Message"]<strong>@_localizer["HUBRFQ"]</strong>?</span>
    </div>
    <form id="release-all-form" class="row common-form mt-2">
        <input type="hidden" name="id" value="@Model.BomId" />
        <input type="hidden" name="code" />
        <input type="hidden" name="name" />
        <input type="hidden" name="company" />
        <input type="hidden" name="companyNo" />
        <input type="hidden" name="requestToPOHubBy" />
        <input type="hidden" name="reqSalesPerson" />
        <input type="hidden" name="supportTeamMemberNo" />
        <input type="hidden" name="requirementId" />
        <div id="release-sourcing-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">@_localizer["SourcingResults"]</span>
            </h3>

            <div id="release-sourcing-content" class="row @contentClasses">
                <table id="release-sourcing-table" class="table simple-table display responsive">
                    <tr>
                        <th></th>
                    </tr>
                </table>
            </div>
        </div>
    </form>
</div>

<script>
    const releaseLocalized = {
        customerRequirementNo: "@_localizer["CustomerRequirementNo"]",
        part: "@_localizer["Part"]",
        sellPrice: "@_localizer["SellPrice"]",
        noData: "@_localizer["NoData"]",
        releaseStatus: "@_localizer["ReleaseStatus"]",
    };
</script>
