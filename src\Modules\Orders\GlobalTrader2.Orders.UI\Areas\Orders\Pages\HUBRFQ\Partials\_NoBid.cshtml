@using Microsoft.Extensions.Localization
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.HUBRFQ.Details

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer

@model IndexModel
<div id="hubrfq-no-bid-dialog" class="dialog-container" title="@_localizer["NoBidTitle"]" style="display: none;">
    <div class="dialog-description">
        <span>@_localizer["Message"]<strong>@_localizer["HUBRFQ"]</strong>?</span>
        <div class="form-error-summary mt-2" style="display: none;">
            <img src="~/img/icons/x-octagon.svg" alt="X-icon" />
            <div>
                <p>@_messageLocalizer["There were some problems with your form."]</p>
                <p>@_messageLocalizer["Please check below and try again."]</p>
                <p></p>
            </div>
        </div>
    </div>
    <form id="no-bid-form" class="row common-form mt-2">
        <input type="hidden" name="id" value="@Model.BomId" />
        <input type="hidden" name="code" />
        <input type="hidden" name="name" />
        <input type="hidden" name="company" />
        <input type="hidden" name="companyNo" />
        <input type="hidden" name="reqSalesPerson" />
        <input type="hidden" name="customerRequirementId" />
        <div class="col-md-12 form-control-wrapper">
            <label for="txtNotes" class="form-label">
                @_localizer["Notes"]<span class="required"> *</span>
            </label>
            <textarea id="txtNotes" name="bidNotes" data-bind-name="bidNotes" class="form-control form-input" rows="2" maxlength="2000"></textarea>
        </div>
    </form>
</div>
